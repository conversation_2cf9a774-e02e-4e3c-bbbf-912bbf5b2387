import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../utils/app_themes.dart';

enum Category { all, recharge, sessions, refund }

class FilterCategory {
  final String id;
  final String name;
  FilterCategory({required this.id, required this.name});
}

class FilterTransactionsSheet extends StatefulWidget {
  final Category initialCategory;
  final Set<String> initialStatuses;
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final void Function(
    Category category,
    Set<String> statuses,
    DateTime? startDate,
    DateTime? endDate,
  ) onApply;

  const FilterTransactionsSheet({
    super.key,
    required this.initialCategory,
    required this.initialStatuses,
    required this.initialStartDate,
    required this.initialEndDate,
    required this.onApply,
  });

  @override
  FilterTransactionsSheetState createState() => FilterTransactionsSheetState();
}

class FilterTransactionsSheetState extends State<FilterTransactionsSheet>
    with SingleTickerProviderStateMixin {
  late Category _selectedCategory;
  late Set<String> _selectedStatuses;
  DateTime? _startDate;
  DateTime? _endDate;

  // Animation controller for staggered animations
  late AnimationController _animationController;

  // List of available categories for filtering
  final List<FilterCategory> _availableCategories = [
    FilterCategory(id: 'all', name: 'All Transactions'),
    FilterCategory(id: 'recharge', name: 'Recharges'),
    FilterCategory(id: 'sessions', name: 'Charging Sessions'),
    FilterCategory(id: 'refund', name: 'Refunds'),
  ];

  // List of available statuses for filtering
  final List<String> _availableStatuses = ['Complete', 'Pending', 'Rejected'];

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.initialCategory;
    _selectedStatuses = Set<String>.from(widget.initialStatuses);
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Start the animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Helper method to map string category ID to enum
  Category _mapCategoryId(String id) {
    switch (id) {
      case 'recharge':
        return Category.recharge;
      case 'sessions':
        return Category.sessions;
      case 'refund':
        return Category.refund;
      default:
        return Category.all;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if dark mode is enabled
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Fixed height for the modal sheet (e.g. 60% of screen)
    final screenHeight = MediaQuery.of(context).size.height;
    return Container(
      height: screenHeight * 0.6,
      // Background with rounded top corners - dark mode aware
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkSurface : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        border: isDarkMode
            ? const Border(
                top: BorderSide(color: AppThemes.darkBorder, width: 1),
                left: BorderSide(color: AppThemes.darkBorder, width: 1),
                right: BorderSide(color: AppThemes.darkBorder, width: 1),
              )
            : null,
      ),
      // SafeArea to handle bottom notches, etc.
      child: SafeArea(
        top: false,
        bottom: true,
        child: Column(
          children: [
            _buildHandleAndClose(),
            // Title with animation
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeOutCubic,
              builder: (context, value, child) {
                return Opacity(
                  opacity: value,
                  child: Transform.translate(
                    offset: Offset(0, 10 * (1 - value)),
                    child: child,
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Filter Transactions',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode
                          ? AppThemes.darkTextPrimary
                          : Colors.grey.shade900,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    children: [
                      // Categories with staggered animation
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeOutCubic,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: child,
                            ),
                          );
                        },
                        child: _buildCategoriesTile(),
                      ),
                      const SizedBox(height: 16),

                      // Status with staggered animation
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: const Duration(milliseconds: 600),
                        curve: Curves.easeOutCubic,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: child,
                            ),
                          );
                        },
                        child: _buildStatusTile(),
                      ),
                      const SizedBox(height: 16),

                      // Date range with staggered animation
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: const Duration(milliseconds: 700),
                        curve: Curves.easeOutCubic,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: child,
                            ),
                          );
                        },
                        child: _buildDateRangeTile(),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
            // Pinned Apply button at the bottom with animation
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeOutCubic,
              builder: (context, value, child) {
                return Opacity(
                  opacity: value,
                  child: Transform.translate(
                    offset: Offset(0, 20 * (1 - value)),
                    child: child,
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                child: _buildApplyButton(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Minimal top area: draggable handle in the middle + close button on the right
  Widget _buildHandleAndClose() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 12),
      child: SizedBox(
        height: 40,
        child: Stack(
          children: [
            // The handle is truly centered horizontally
            Align(
              alignment: Alignment.center,
              child: Container(
                width: 40,
                height: 5,
                decoration: BoxDecoration(
                  color:
                      isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
            ),
            // Close button pinned to the right
            Positioned(
              right: 8,
              top: 0,
              bottom: 0,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? AppThemes.darkCard
                          : Colors.grey.shade200,
                      shape: BoxShape.circle,
                      border: isDarkMode
                          ? Border.all(color: AppThemes.darkBorder, width: 1)
                          : null,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 20,
                      color: isDarkMode
                          ? AppThemes.darkTextSecondary
                          : Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Categories tile
  Widget _buildCategoriesTile() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkCard : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: isDarkMode
              ? Border.all(color: AppThemes.darkBorder, width: 1)
              : null,
          boxShadow: isDarkMode
              ? null
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Padding(
              padding: const EdgeInsets.only(
                left: 20,
                right: 20,
                top: 16,
                bottom: 8,
              ),
              child: Text(
                'Categories',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode
                      ? AppThemes.darkTextPrimary
                      : Colors.grey.shade800,
                ),
              ),
            ),
            Column(
              children: List.generate(_availableCategories.length, (index) {
                final cat = _availableCategories[index];
                final isLast = index == _availableCategories.length - 1;

                // Staggered animation for each category
                return TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: Duration(milliseconds: 400 + (index * 100)),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Transform.translate(
                        offset: Offset(20 * (1 - value), 0),
                        child: child,
                      ),
                    );
                  },
                  child: Column(
                    children: [
                      _buildCategoryItem(cat),
                      if (!isLast) _buildThinDivider(),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(FilterCategory cat) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bool isSelected = _mapCategoryId(cat.id) == _selectedCategory;

    // Get icon based on category
    IconData categoryIcon;
    if (cat.id == 'recharge') {
      categoryIcon = Icons.account_balance_wallet;
    } else if (cat.id == 'refund') {
      categoryIcon = Icons.replay;
    } else if (cat.id == 'sessions') {
      categoryIcon = Icons.bolt;
    } else {
      categoryIcon = Icons.list;
    }

    return InkWell(
      onTap: () {
        setState(() {
          _selectedCategory = _mapCategoryId(cat.id);
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        child: Row(
          children: [
            // Radio button with animation
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color:
                    isSelected ? AppThemes.secondaryColor : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected
                      ? AppThemes.secondaryColor
                      : isDarkMode
                          ? AppThemes.darkTextSecondary
                          : Colors.grey.shade400,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? const Icon(Icons.check, size: 16, color: Colors.white)
                  : null,
            ),
            const SizedBox(width: 16),
            // Category icon
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppThemes.secondaryColor.withAlpha(26) // 0.1 * 255 = ~26
                    : isDarkMode
                        ? AppThemes.darkBorder
                        : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                categoryIcon,
                size: 18,
                color: isSelected
                    ? AppThemes.secondaryColor
                    : isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade600,
              ),
            ),
            const SizedBox(width: 12),
            // Category name
            Text(
              cat.name,
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode
                    ? (isSelected
                        ? AppThemes.darkTextPrimary
                        : AppThemes.darkTextSecondary)
                    : (isSelected ? Colors.black : Colors.grey.shade700),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThinDivider() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      height: 1,
      color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade200,
      margin: const EdgeInsets.symmetric(horizontal: 20),
    );
  }

  // Status tile
  Widget _buildStatusTile() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkCard : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: isDarkMode
              ? Border.all(color: AppThemes.darkBorder, width: 1)
              : null,
          boxShadow: isDarkMode
              ? null
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode
                    ? AppThemes.darkTextPrimary
                    : Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 12),
            // Changed from Column to Row to display status buttons horizontally
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: _availableStatuses.map((status) {
                final isSelected = _selectedStatuses.contains(status);

                // Determine color and icon based on status
                Color statusColor;
                IconData statusIcon;

                if (status == 'Complete') {
                  statusColor = Colors.green;
                  statusIcon = Icons.check_circle;
                } else if (status == 'Pending') {
                  statusColor = Colors.orange;
                  statusIcon = Icons.pending;
                } else {
                  statusColor = Colors.red;
                  statusIcon = Icons.cancel;
                }

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedStatuses.remove(status);
                      } else {
                        _selectedStatuses.add(status);
                      }
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 7,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? statusColor.withAlpha(26) // 0.1 * 255 = ~26
                          : isDarkMode
                              ? AppThemes.darkBorder
                              : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? statusColor
                            : isDarkMode
                                ? AppThemes.darkTextTertiary
                                : Colors.grey.shade300,
                        width: 1.5,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isSelected ? statusIcon : Icons.circle_outlined,
                          size: 16,
                          color: isSelected
                              ? statusColor
                              : isDarkMode
                                  ? AppThemes.darkTextSecondary
                                  : Colors.grey.shade400,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          status,
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w400,
                            color: isSelected
                                ? statusColor
                                : isDarkMode
                                    ? AppThemes.darkTextSecondary
                                    : Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  // Date Range tile
  Widget _buildDateRangeTile() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final dateFormat = DateFormat('dd MMM yyyy');

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkCard : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: isDarkMode
              ? Border.all(color: AppThemes.darkBorder, width: 1)
              : null,
          boxShadow: isDarkMode
              ? null
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.date_range,
                  size: 20,
                  color: isDarkMode
                      ? AppThemes.primaryColor
                      : AppThemes.secondaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Date Range',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode
                        ? AppThemes.darkTextPrimary
                        : Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    label: 'From',
                    date: _startDate,
                    onTap: () => _selectDate(true),
                    dateFormat: dateFormat,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    label: 'To',
                    date: _endDate,
                    onTap: () => _selectDate(false),
                    dateFormat: dateFormat,
                  ),
                ),
              ],
            ),
            if (_startDate != null && _endDate != null) ...[
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _startDate = null;
                      _endDate = null;
                    });
                  },
                  icon: Icon(
                    Icons.clear,
                    size: 18,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade700,
                  ),
                  label: Text(
                    'Clear Dates',
                    style: TextStyle(
                      color: isDarkMode
                          ? AppThemes.darkTextSecondary
                          : Colors.grey.shade700,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required DateFormat dateFormat,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
              fontSize: 14,
              color: isDarkMode
                  ? AppThemes.darkTextSecondary
                  : Colors.grey.shade600),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isDarkMode ? AppThemes.darkSurface : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color:
                      isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                  width: 1),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    date != null ? dateFormat.format(date) : 'Select Date',
                    style: TextStyle(
                      fontSize: 15,
                      color: date != null
                          ? (isDarkMode
                              ? AppThemes.darkTextPrimary
                              : Colors.black)
                          : (isDarkMode
                              ? AppThemes.darkTextTertiary
                              : Colors.grey.shade500),
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today_rounded,
                  size: 18,
                  color: isDarkMode
                      ? AppThemes.darkTextSecondary
                      : Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final initialDate = isStartDate
        ? (_startDate ?? DateTime.now())
        : (_endDate ?? DateTime.now());
    final firstDate =
        isStartDate ? DateTime(2020) : (_startDate ?? DateTime(2020));
    final lastDate = isStartDate
        ? (_endDate ?? DateTime.now().add(const Duration(days: 365)))
        : DateTime.now().add(const Duration(days: 365));

    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: isDarkMode
                ? ColorScheme.dark(
                    primary: AppThemes.primaryColor,
                    onPrimary: Colors.white,
                    surface: AppThemes.darkSurface,
                    onSurface: AppThemes.darkTextPrimary,
                  )
                : ColorScheme.light(
                    primary: AppThemes.secondaryColor,
                    onPrimary: Colors.white,
                    onSurface: Colors.black,
                  ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: isDarkMode
                    ? AppThemes.primaryColor
                    : AppThemes.secondaryColor,
              ),
            ),
            dialogTheme: DialogThemeData(
              backgroundColor:
                  isDarkMode ? AppThemes.darkSurface : Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        if (isStartDate) {
          _startDate = pickedDate;
          // If end date is before start date, update end date
          if (_endDate != null && _endDate!.isBefore(_startDate!)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = pickedDate;
        }
      });
    }
  }

  Widget _buildApplyButton() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () {
          // Ensure we have at least one status selected to prevent filter issues
          final statuses = _selectedStatuses.isEmpty
              ? Set<String>.from(
                  _availableStatuses) // If none selected, use all
              : _selectedStatuses;

          widget.onApply(
            _selectedCategory,
            statuses,
            _startDate,
            _endDate,
          );
          Navigator.pop(context);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppThemes
              .primaryColor, // Use primary color for both light and dark mode
          foregroundColor: Colors.white,
          elevation: isDarkMode ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.filter_list, size: 20),
            const SizedBox(width: 8),
            Text(
              'Apply Filters',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
