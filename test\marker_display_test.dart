import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/screens/dashboard/dashboard_horizontal_cards.dart';

void main() {
  group('Marker Display Tests', () {
    test('Station ID mapping should work correctly', () {
      // Test data simulating the two different data sources
      final filteredStations = [
        {
          'id': '123',
          'name': 'Test Station 1',
          'latitude': 12.9716,
          'longitude': 77.5946,
        },
        {
          'id': '456',
          'name': 'Test Station 2',
          'latitude': 12.9726,
          'longitude': 77.5956,
        },
      ];

      final allMapStations = [
        {
          'id': '123',
          'name': 'Test Station 1',
          'latitude': 12.9716,
          'longitude': 77.5946,
        },
        {
          'id': '789', // Different ID but same coordinates
          'name': 'Test Station 2',
          'latitude': 12.9726,
          'longitude': 77.5956,
        },
      ];

      // Test direct ID match
      expect(
        filteredStations.any((station) => 
          allMapStations.any((mapStation) => 
            station['id'] == mapStation['id'])),
        isTrue,
        reason: 'Should find direct ID match for station 123'
      );

      // Test coordinate-based matching
      final station2Filtered = filteredStations[1];
      Map<String, dynamic>? matchingMapStation;
      try {
        matchingMapStation = allMapStations.firstWhere(
          (mapStation) {
            final latDiff = ((station2Filtered['latitude'] as double) -
                            (mapStation['latitude'] as double)).abs();
            final lngDiff = ((station2Filtered['longitude'] as double) -
                            (mapStation['longitude'] as double)).abs();
            return latDiff < 0.0001 && lngDiff < 0.0001;
          },
        );
      } catch (e) {
        matchingMapStation = null;
      }

      expect(
        matchingMapStation != null && matchingMapStation.isNotEmpty,
        isTrue,
        reason: 'Should find coordinate match for station with different ID'
      );
    });

    test('Marker focus state should be trackable', () {
      String? currentFocusedStationId;
      
      // Simulate focusing on a station
      currentFocusedStationId = '123';
      expect(currentFocusedStationId, equals('123'));
      
      // Simulate changing focus
      currentFocusedStationId = '456';
      expect(currentFocusedStationId, equals('456'));
      
      // Simulate clearing focus
      currentFocusedStationId = null;
      expect(currentFocusedStationId, isNull);
    });

    test('Station data should have required marker fields', () {
      final stationData = {
        'id': '123',
        'name': 'Test Station',
        'latitude': 12.9716,
        'longitude': 77.5946,
        'status': 'Available',
        'mapPinUrl': 'https://api2.eeil.online/mapicons/ecoplug_available.png',
        'focusedMapPinUrl': 'https://api2.eeil.online/mapicons/ecoplug_focus.png',
      };

      // Verify required fields exist
      expect(stationData.containsKey('id'), isTrue);
      expect(stationData.containsKey('latitude'), isTrue);
      expect(stationData.containsKey('longitude'), isTrue);
      expect(stationData.containsKey('mapPinUrl'), isTrue);
      expect(stationData.containsKey('focusedMapPinUrl'), isTrue);

      // Verify marker URLs are valid
      expect(stationData['mapPinUrl'], isNotNull);
      expect(stationData['focusedMapPinUrl'], isNotNull);
      expect((stationData['mapPinUrl'] as String).isNotEmpty, isTrue);
      expect((stationData['focusedMapPinUrl'] as String).isNotEmpty, isTrue);
    });

    test('Coordinate matching should work with different precision levels', () {
      final station1 = {'latitude': 12.9716, 'longitude': 77.5946};
      final station2 = {'latitude': 12.97161, 'longitude': 77.59461}; // Very close
      final station3 = {'latitude': 12.9726, 'longitude': 77.5956}; // Further away

      final precisionLevels = [0.00001, 0.0001, 0.001, 0.01]; // Added higher precision

      // Test very close coordinates
      bool closeMatch = false;
      for (final precision in precisionLevels) {
        final latDiff = ((station1['latitude']! - station2['latitude']!).abs());
        final lngDiff = ((station1['longitude']! - station2['longitude']!).abs());

        if (latDiff < precision && lngDiff < precision) {
          closeMatch = true;
          break;
        }
      }
      expect(closeMatch, isTrue, reason: 'Should match very close coordinates');

      // Test further coordinates - calculate actual distance first
      final latDiff = ((station1['latitude']! - station3['latitude']!).abs());
      final lngDiff = ((station1['longitude']! - station3['longitude']!).abs());

      // The actual distance is approximately 0.001 for lat and 0.001 for lng (with floating point precision)
      expect(latDiff, closeTo(0.001, 0.0001));
      expect(lngDiff, closeTo(0.001, 0.0001));

      // Should match with precision 0.01 (which is higher than ~0.001)
      bool foundMatch = false;
      for (final precision in precisionLevels) {
        if (latDiff < precision && lngDiff < precision) {
          foundMatch = true;
          break;
        }
      }

      expect(foundMatch, isTrue, reason: 'Should match with precision 0.01 (distance is ~0.001)');
    });
  });
}
