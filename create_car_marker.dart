import 'dart:io';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

// Temporary script to create a car marker icon
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Create a car marker icon
  final bytes = await createCarMarkerIcon();
  
  // Save to assets directory
  final file = File('assets/icons/cars/car marker icons.png');
  await file.create(recursive: true);
  await file.writeAsBytes(bytes);
  
  print('Car marker icon created at: ${file.path}');
}

Future<Uint8List> createCarMarkerIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  const size = 100.0;
  
  // Draw car shape
  final paint = Paint()
    ..color = Colors.blue
    ..style = PaintingStyle.fill;
  
  // Car body (main rectangle)
  canvas.drawRRect(
    RRect.fromRectAndRadius(
      Rect.fromLTWH(size * 0.2, size * 0.4, size * 0.6, size * 0.3),
      const Radius.circular(8),
    ),
    paint,
  );
  
  // Car roof
  canvas.drawRRect(
    RRect.fromRectAndRadius(
      Rect.fromLTWH(size * 0.3, size * 0.25, size * 0.4, size * 0.25),
      const Radius.circular(6),
    ),
    paint,
  );
  
  // Wheels
  final wheelPaint = Paint()
    ..color = Colors.black
    ..style = PaintingStyle.fill;
  
  canvas.drawCircle(Offset(size * 0.3, size * 0.75), size * 0.08, wheelPaint);
  canvas.drawCircle(Offset(size * 0.7, size * 0.75), size * 0.08, wheelPaint);
  
  // Convert to image
  final picture = recorder.endRecording();
  final image = await picture.toImage(size.toInt(), size.toInt());
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  
  return byteData!.buffer.asUint8List();
}
