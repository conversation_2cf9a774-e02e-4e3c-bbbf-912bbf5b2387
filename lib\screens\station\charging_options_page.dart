import 'package:flutter/material.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/screens/charging_initialization_screen.dart';
import 'package:ecoplug/utils/app_themes.dart';

import 'package:ecoplug/services/charging_parameters_service.dart';
import 'package:ecoplug/features/wallet/services/wallet_service.dart';
import 'package:ecoplug/core/services/service_locator.dart';

import '../../services/charging_session_service.dart';
// PayU payment integration

// Enum for charging option types
enum ChargingOptionType {
  unitBased,
  amountBased,
}

/// ChargingOptionsPage handles the UI and logic for configuring a charging session.
/// It allows users to select between unit-based and amount-based charging options.
class ChargingOptionsPage extends StatefulWidget {
  final Connector connector;

  const ChargingOptionsPage({
    super.key,
    required this.connector,
  });

  @override
  State<ChargingOptionsPage> createState() => _ChargingOptionsPageState();
}

class _ChargingOptionsPageState extends State<ChargingOptionsPage> {
  // Charging option selection
  ChargingOptionType _selectedOption = ChargingOptionType.unitBased;

  // Values for unit and amount based charging
  double _unitValue = 10.0; // Default 10 kWh
  double _amountValue = 200.0; // Default ₹200

  // Text controllers for manual input in amount section
  late TextEditingController _valueInputController;
  late TextEditingController _amountInputController;
  bool _isCalculating = false; // Flag to prevent circular updates

  // Store calculated units from hidden calculation for API parameters
  double _calculatedUnits = 0.0;

  // CHARGING MODE: true = Normal (production), false = Direct (testing/development)
  // Normal mode: Full production API calls and real charging flow
  // Only normal mode charging supported - direct mode removed

  // Wallet balance state
  double _walletBalance = 0.0;
  bool _isLoadingWallet = true;
  String? _walletError;
  late WalletService _walletService;

  // Min and max values for sliders - REMOVED AMOUNT RESTRICTION
  final double _minUnit = 1.0;
  final double _maxUnit = 50.0;
  final double _minAmount =
      50.0; // Restored 50 rupees minimum amount requirement
  double _maxAmount = 1000.0; // Will be updated based on wallet balance

  // GST and wallet balance constants
  static const double _gstPercentage = 18.0; // GST percentage
  static const double _usableWalletPercentage =
      82.0; // Usable wallet percentage for base amount

  @override
  void initState() {
    super.initState();
    _walletService = ServiceLocator().walletService;

    // Initialize text controllers with default values
    _valueInputController =
        TextEditingController(text: _amountValue.toStringAsFixed(0));
    _amountInputController = TextEditingController();

    // Perform initial hidden calculation based on default value and connector price
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performHiddenCalculation(_amountValue, widget.connector);
    });

    _fetchWalletBalance();
  }

  @override
  void dispose() {
    _valueInputController.dispose();
    _amountInputController.dispose();
    super.dispose();
  }

  /// Fetch wallet balance and update max amount for amount-based charging
  Future<void> _fetchWalletBalance() async {
    try {
      setState(() {
        _isLoadingWallet = true;
        _walletError = null;
      });

      final walletResponse = await _walletService.getWalletInfo();

      if (walletResponse.success && walletResponse.data != null) {
        final balance = walletResponse.data!.balance;
        final maxUsableAmount = (balance * _usableWalletPercentage / 100);

        setState(() {
          _walletBalance = balance;
          _maxAmount =
              maxUsableAmount; // Pure usable amount calculation, no minimum override
          _isLoadingWallet = false;

          // Adjust current amount value if it exceeds the new max
          if (_amountValue > _maxAmount) {
            _amountValue = _maxAmount;
          }
        });

        // Check minimum balance requirement after loading wallet data
        if (!_validateMinimumBalance()) {
          _showInsufficientBalanceDialog();
        }
      } else {
        setState(() {
          _walletError = 'Failed to load wallet balance';
          _isLoadingWallet = false;
        });
      }
    } catch (e) {
      setState(() {
        _walletError = 'Error loading wallet balance: $e';
        _isLoadingWallet = false;
      });
    }
  }

  /// Calculate GST amount for a given base amount
  double _calculateGST(double baseAmount) {
    return baseAmount * _gstPercentage / 100;
  }

  /// Calculate total amount including GST
  double _calculateTotalWithGST(double baseAmount) {
    return baseAmount + _calculateGST(baseAmount);
  }

  /// Check if amount is within wallet balance limits
  bool _isAmountValid(double amount) {
    final totalWithGST = _calculateTotalWithGST(amount);
    return totalWithGST <= _walletBalance;
  }

  /// Validate if user has sufficient balance for minimum charging amount
  bool _validateMinimumBalance() {
    const double minimumBalance = 50.0; // Minimum 50 rupees required
    debugPrint(
        'Validating minimum balance: ₹$_walletBalance (minimum: ₹$minimumBalance)');
    return _walletBalance >= minimumBalance;
  }

  /// Show dialog when user has insufficient balance for charging
  void _showInsufficientBalanceDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Insufficient Balance'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your current wallet balance is ₹${_walletBalance.toStringAsFixed(2)}.',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 8),
              const Text(
                'A minimum balance of ₹50 is required to start charging.',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              const Text(
                'Please add money to your wallet to continue.',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Go back to previous screen
              },
              child: const Text('Go Back'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Go back to previous screen
                // TODO: Navigate to wallet/add money screen
              },
              child: const Text('Add Money'),
            ),
          ],
        );
      },
    );
  }

  /// Update value input controller when amount value changes from slider
  void _updateValueInputFromSlider() {
    if (_isCalculating) return;

    _isCalculating = true;
    _valueInputController.text = _amountValue.toStringAsFixed(0);
    _isCalculating = false;
  }

  /// Hidden calculation logic - performs amount to units conversion in background
  /// This implements the logic: if (chargeType == 'amount' || instantCharging) { amount = value / price_per_unit }
  void _performHiddenCalculation(double monetaryValue, Connector connector) {
    try {
      final pricePerUnit = _getAuthenticPricePerUnit(connector);
      if (pricePerUnit <= 0) {
        _calculatedUnits = 0.0;
        return;
      }

      // Calculate units from monetary amount (hidden from UI)
      final calculatedUnits = monetaryValue / pricePerUnit;

      // Store the calculated units for API parameters
      _calculatedUnits = calculatedUnits;

      // Also store in controller for internal use (not displayed in UI)
      _amountInputController.text = calculatedUnits.toStringAsFixed(2);
    } catch (e) {
      _calculatedUnits = 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get connector details
    final connector = widget.connector;
    final connectorType = connector.type;

    // Use unified app theme color for all connectors
    final Color primaryColor =
        AppThemes.primaryColor; // Use app theme lime green

    // Check if dark mode is enabled
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF121212) : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Start Charging',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Configure your charging session',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode
                            ? Colors.grey.shade400
                            : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                // Close button
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? Colors.grey.withAlpha(15)
                          : Colors.grey.withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 20,
                      color: isDarkMode
                          ? Colors.grey.shade300
                          : Colors.grey.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Connector info section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Connector details
                Row(
                  children: [
                    // Connector icon
                    Container(
                      width: 38,
                      height: 38,
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.grey.withAlpha(15)
                            : Colors.grey.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: (connector.icon != null &&
                                connector.icon!.isNotEmpty)
                            ? Image.network(
                                connector.icon!,
                                width: 24,
                                height: 24,
                                color: isDarkMode ? Colors.white : null,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    connectorType.contains("DC")
                                        ? Icons.flash_on
                                        : Icons.electrical_services,
                                    color: primaryColor,
                                    size: 20,
                                  );
                                },
                              )
                            : Icon(
                                connectorType.contains("DC")
                                    ? Icons.flash_on
                                    : Icons.electrical_services,
                                color: primaryColor,
                                size: 20,
                              ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Connector name and details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.connector.name, // Use connector name
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _getFormattedPower(widget
                                .connector), // CRITICAL: Use authentic API power data
                            style: TextStyle(
                              fontSize: 13,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Connector specifications
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.withAlpha(15)
                        : Colors.grey.withAlpha(8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Power
                      Column(
                        children: [
                          Icon(Icons.bolt, color: primaryColor, size: 20),
                          const SizedBox(height: 8),
                          Text(
                            _getFormattedPower(widget
                                .connector), // CRITICAL: Use authentic API power data
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'Power',
                            style: TextStyle(
                              fontSize: 12,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),

                      // Price
                      Column(
                        children: [
                          Icon(Icons.currency_rupee,
                              color: primaryColor, size: 20),
                          const SizedBox(height: 8),
                          Text(
                            widget.connector.priceLabel ??
                                'Price not available', // ONLY use authentic API priceLabel
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'Price',
                            style: TextStyle(
                              fontSize: 12,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),

                      // Est. Time
                      Column(
                        children: [
                          Icon(Icons.access_time,
                              color: primaryColor, size: 20),
                          const SizedBox(height: 8),
                          Text(
                            'N/A', // Est. Time - No API field, set to N/A
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'Est. Time',
                            style: TextStyle(
                              fontSize: 12,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Charging options section
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Charging Options',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),

                // Charging options
                Row(
                  children: [
                    // Unit Based Option
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _selectedOption = ChargingOptionType.unitBased;
                          });
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color:
                                _selectedOption == ChargingOptionType.unitBased
                                    ? primaryColor.withAlpha(20)
                                    : isDarkMode
                                        ? Colors.grey.withAlpha(15)
                                        : Colors.grey.withAlpha(8),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _selectedOption ==
                                      ChargingOptionType.unitBased
                                  ? primaryColor
                                  : Colors.grey.withAlpha(40),
                              width: _selectedOption ==
                                      ChargingOptionType.unitBased
                                  ? 2
                                  : 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.bolt,
                                color: _selectedOption ==
                                        ChargingOptionType.unitBased
                                    ? primaryColor
                                    : Colors.grey.shade600,
                                size: 18,
                              ),
                              const SizedBox(height: 6),
                              Text(
                                'Unit Based',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 13,
                                  color: _selectedOption ==
                                          ChargingOptionType.unitBased
                                      ? primaryColor
                                      : isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'Set kWh units',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Amount Based Option
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _selectedOption = ChargingOptionType.amountBased;
                          });
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _selectedOption ==
                                    ChargingOptionType.amountBased
                                ? primaryColor.withAlpha(20)
                                : isDarkMode
                                    ? Colors.grey.withAlpha(15)
                                    : Colors.grey.withAlpha(8),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _selectedOption ==
                                      ChargingOptionType.amountBased
                                  ? primaryColor
                                  : Colors.grey.withAlpha(40),
                              width: _selectedOption ==
                                      ChargingOptionType.amountBased
                                  ? 2
                                  : 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.currency_rupee,
                                color: _selectedOption ==
                                        ChargingOptionType.amountBased
                                    ? primaryColor
                                    : Colors.grey.shade600,
                                size: 18,
                              ),
                              const SizedBox(height: 6),
                              Text(
                                'Amount Based',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 13,
                                  color: _selectedOption ==
                                          ChargingOptionType.amountBased
                                      ? primaryColor
                                      : isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'Set price limit',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Option configuration section
          Container(
            margin: const EdgeInsets.fromLTRB(20, 16, 20, 0),
            child:
                _buildOptionConfiguration(primaryColor, connector, isDarkMode),
          ),

          // Normal mode charging only - direct mode removed

          // Start charging button with improved design
          Container(
            margin: const EdgeInsets.fromLTRB(20, 16, 20, 20),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  _startCharging(context, connector);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                  shadowColor: primaryColor.withAlpha(50),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.electric_bolt,
                      size: 22,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      'Start Charging',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Start charging action
  void _startCharging(BuildContext contextParam, Connector connector) {
    // Store the BuildContext for later use
    final BuildContext context = contextParam;

    // First, validate minimum balance requirement
    if (!_validateMinimumBalance()) {
      debugPrint(
          '❌ Minimum balance validation failed - showing insufficient balance dialog');
      _showInsufficientBalanceDialog();
      return;
    }

    // Validate wallet balance for amount-based charging
    if (_selectedOption == ChargingOptionType.amountBased) {
      final totalWithGST = _calculateTotalWithGST(_amountValue);

      if (totalWithGST > _walletBalance) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Insufficient wallet balance. Total amount (₹${totalWithGST.toStringAsFixed(0)}) exceeds your wallet balance (₹${_walletBalance.toStringAsFixed(2)})',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }

      if (_amountValue > _maxAmount) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Amount exceeds the maximum allowed (₹${_maxAmount.toStringAsFixed(0)}). Please select a lower amount.',
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }
    }

    // Show loading indicator
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // Start the charging process
    _performChargingStart(context, connector);
  }

  /// CRITICAL: Get formatted power from authentic API data
  String _getFormattedPower(Connector connector) {
    // Use maxElectricPower from authentic API data
    if (connector.maxElectricPower != null && connector.maxElectricPower! > 0) {
      return '${connector.maxElectricPower} kW';
    }

    // Fallback to power string if available
    if (connector.power != null && connector.power!.isNotEmpty) {
      final powerStr = connector.power!;
      // If it already contains kW, return as is
      if (powerStr.toLowerCase().contains('kw')) {
        return powerStr;
      }
      // If it's just a number, add kW
      final powerNum = double.tryParse(powerStr);
      if (powerNum != null) {
        return '${powerNum.toStringAsFixed(0)} kW';
      }
      return powerStr;
    }

    return 'Power not available';
  }

  /// CRITICAL: Get authentic price per unit from API data
  double _getAuthenticPricePerUnit(Connector connector) {
    // Use pricePerUnit from authentic API data
    if (connector.pricePerUnit != null && connector.pricePerUnit! > 0) {
      return connector.pricePerUnit!.toDouble();
    }

    // Fallback to price if available
    if (connector.price != null && connector.price! > 0) {
      return connector.price!.toDouble();
    }

    // No authentic price data available
    return 0.0;
  }

  // Perform the actual charging start process - Normal mode only
  Future<void> _performChargingStart(
      BuildContext contextParam, Connector connector) async {
    // Store the context in a local variable
    final context = contextParam;

    // Pop the loading dialog shown in _startCharging
    if (Navigator.canPop(context)) {
      Navigator.pop(context); // Dismiss loading dialog
    }

    // NORMAL MODE: Full production API calls and real charging flow
    await _performNormalModeCharging(context, connector);
  }

  /// NORMAL MODE: Full production charging flow with real API calls
  Future<void> _performNormalModeCharging(
      BuildContext context, Connector connector) async {
    // CRITICAL: First check connector connection before proceeding
    await _checkConnectorConnectionAndProceed(context, connector);
  }

  /// Check connector connection using first process API before proceeding
  Future<void> _checkConnectorConnectionAndProceed(
      BuildContext context, Connector connector) async {
    // Show connection checking dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF43A047)),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Checking Connector Connection',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Please ensure your vehicle is connected to the charging port',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      );
    }

    try {
      // CRITICAL: Prepare authentic charging parameters from API data
      final chargingParams = _prepareAuthenticChargingParams(connector);

      // Validate EVSE UID before API call
      final String evseUidToUse = connector.evsesUid ?? 'unknown';

      if (evseUidToUse == 'unknown' || evseUidToUse.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: Invalid EVSE UID detected')),
          );
        }
        return;
      }

      final chargingService = ChargingSessionService();
      final connectionResponse = await chargingService.startChargingSession(
        evseUid: evseUidToUse,
        connectorId: connector.id,
        chargingValue: chargingParams['chargingValue'],
        instantCharging: chargingParams['instantCharging'],
      );

      // Dismiss the checking dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (connectionResponse.success) {
        // Gun is connected - proceed with charging flow
        debugPrint('✅ Gun connection verified - proceeding to initialization');
        debugPrint('✅ Response data: ${connectionResponse.data}');

        // 🚨 CRITICAL: Store transaction ID for Step 2 verification
        if (connectionResponse.data != null) {
          final transactionId = connectionResponse.data!['id']?.toString();
          if (transactionId != null && transactionId.isNotEmpty) {
            final chargingParamsService = ChargingParametersService();
            chargingParamsService.storeTransactionId(transactionId);
            debugPrint('🔒 STORED TRANSACTION ID: $transactionId');
          } else {
            debugPrint('❌ WARNING: No transaction ID in response');
          }
        }

        if (mounted) {
          await _proceedToChargingInitialization(
              context, connector, chargingParams);
        }
      } else {
        // Gun connection failed - handle error with actual API message
        debugPrint('❌ Gun connection failed: ${connectionResponse.message}');
        if (mounted) {
          await _handleConnectorConnectionError(
              context, connectionResponse.message);
        }
      }
    } catch (e) {
      // Dismiss the checking dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      debugPrint('❌ Error checking gun connection: $e');
      if (mounted) {
        await _handleConnectorConnectionError(
            context, 'Failed to check gun connection: $e');
      }
    }
  }

  /// Proceed to charging initialization after successful connector connection
  Future<void> _proceedToChargingInitialization(BuildContext context,
      Connector connector, Map<String, dynamic> chargingParams) async {
    debugPrint('🚀 Proceeding to charging initialization...');

    // Store charging parameters globally for use in charging flow
    _storeChargingParametersGlobally(chargingParams, connector);

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Gun connected - Starting charging session'),
          backgroundColor: Color(0xFF43A047),
          duration: Duration(seconds: 2),
        ),
      );

      // Small delay to show the snackbar
      await Future.delayed(const Duration(milliseconds: 500));

      // Navigate to initialization screen with Normal Mode
      debugPrint('🔌 NORMAL MODE: Navigating to initialization screen');
      debugPrint(
          '🔌 CRITICAL: Session already started - passing sessionAlreadyStarted: true');

      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => ChargingInitializationScreen(
              stationUid: connector.evsesUid ?? 'unknown',
              connectorId: connector.id ?? 'unknown',
              // Direct mode removed - only normal mode supported
              sessionAlreadyStarted:
                  true, // 🚨 CRITICAL: Prevent duplicate API call
            ),
          ),
        );
      }
    }
  }

  /// Handle server-side error messages from API response
  Future<void> _handleConnectorConnectionError(
      BuildContext context, String? errorMessage) async {
    if (!mounted) return;

    // Display actual server-side error message from API response
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          contentPadding: EdgeInsets.zero,
          content: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Server error header with bell icon
                Container(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // Bell alert icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.notifications_active,
                          color: Colors.red[600],
                          size: 40,
                        ),
                      ),
                      const SizedBox(height: 20),
                      // Server error message
                      Text(
                        errorMessage ?? 'Server Error',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Clean action buttons
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.pop(context); // Close error dialog
                            Navigator.pop(
                                context); // Close charging options page
                          },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text(
                            'Cancel',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context); // Close error dialog
                            // Small delay then retry
                            Future.delayed(const Duration(milliseconds: 500),
                                () {
                              if (mounted) {
                                _performNormalModeCharging(
                                    context, widget.connector);
                              }
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: const Text(
                            'Try Again',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Direct mode removed - only normal mode charging supported

  /// NORMAL MODE: Prepare authentic charging parameters from API data
  Map<String, dynamic> _prepareAuthenticChargingParams(Connector connector) {
    // Get authentic price per unit from API
    final authenticPricePerUnit = _getAuthenticPricePerUnit(connector);

    // Get authentic max power from API
    final authenticMaxPower = connector.maxElectricPower?.toDouble() ?? 0.0;

    // Determine charging value based on selected option
    double chargingValue;
    String chargeType;
    bool instantCharging = false;

    if (_selectedOption == ChargingOptionType.unitBased) {
      chargingValue = _unitValue; // Use selected kWh value
      chargeType = 'units';
    } else {
      chargingValue = _amountValue; // Use selected amount value (₹)
      chargeType = 'amount';
    }

    // Apply the logic: if (chargeType == 'amount' || instantCharging) { amount = value / price_per_unit }
    double finalChargingValue = chargingValue;
    if (chargeType == 'amount' || instantCharging) {
      finalChargingValue =
          _calculatedUnits; // Use calculated units (kWh) for API
    }

    debugPrint('🔌 NORMAL MODE: AUTHENTIC CHARGING PARAMETERS:');
    debugPrint(
        '  Original Value: $chargingValue (${chargeType == 'amount' ? '₹' : 'kWh'})');
    debugPrint('  Final Charging Value: $finalChargingValue kWh');
    debugPrint('  Charge Type: $chargeType');
    debugPrint('  Price Per Unit: ₹$authenticPricePerUnit');
    debugPrint('  Max Power: ${authenticMaxPower}kW');
    debugPrint('  Connector Type: ${connector.type}');
    debugPrint('  EVSE UID: ${connector.evsesUid ?? 'NO_EVSE_UID'}');

    return {
      'chargingValue': finalChargingValue, // Pass calculated units (kWh) to API
      'chargeType': chargeType,
      'pricePerUnit': authenticPricePerUnit,
      'maxPower': authenticMaxPower,
      'instantCharging': instantCharging,
    };
  }

  /// Store charging parameters globally for use throughout charging flow
  void _storeChargingParametersGlobally(
      Map<String, dynamic> chargingParams, Connector connector) {
    // Add connector-specific data to the parameters
    final completeParams = Map<String, dynamic>.from(chargingParams);
    completeParams['connectorType'] = connector.type;
    completeParams['evsesUid'] = connector.evsesUid ?? 'MOCK_EVSE_UID';
    completeParams['connectorId'] = connector.id;
    completeParams['connectorName'] = connector.name;
    completeParams['connectorStatus'] = connector.status;
    completeParams['chargingMode'] = 'normal'; // Only normal mode supported

    // Store in global service for use throughout charging flow
    final chargingParamsService = ChargingParametersService();
    chargingParamsService.storeChargingParameters(completeParams);

    // Log the parameters for debugging
    debugPrint('Charging parameters: $completeParams');

    debugPrint(
        '✅ NORMAL MODE: STORED COMPLETE AUTHENTIC CHARGING PARAMETERS GLOBALLY');
  }

  // Unused helper methods removed

  // Helper method to build option configuration based on selected type
  Widget _buildOptionConfiguration(
      Color primaryColor, Connector connector, bool isDarkMode) {
    switch (_selectedOption) {
      case ChargingOptionType.unitBased:
        return _buildUnitConfiguration(primaryColor, connector, isDarkMode);
      case ChargingOptionType.amountBased:
        return _buildAmountConfiguration(primaryColor, connector, isDarkMode);
    }
  }

  // Unit-based configuration with enhanced eco-friendly design
  Widget _buildUnitConfiguration(
      Color primaryColor, Connector connector, bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Set Charging Units',
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
            letterSpacing: -0.3,
          ),
        ),
        const SizedBox(height: 12),

        // Unit value display with eco-friendly background images
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.grey.withAlpha(15)
                : primaryColor.withAlpha(8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: primaryColor.withAlpha(30),
              width: 1,
            ),
          ),
          child: Stack(
            children: [
              // Background eco-friendly imagery with appropriate colors
              Positioned.fill(
                child: Opacity(
                  opacity:
                      0.12, // Slightly more visible background images - opacity is fine here as it's used with Opacity widget
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      // Cloud icon with cloud color
                      Icon(
                        Icons.cloud_outlined,
                        size: 48,
                        color: const Color(0xFF90A4AE), // Cloud color
                      ),
                      // Leaf icon with leaf green color
                      Icon(
                        Icons.eco,
                        size: 48,
                        color: AppThemes.primaryColor, // App theme green color
                      ),
                      // Electric icon with electric blue color
                      Icon(
                        Icons.energy_savings_leaf_outlined,
                        size: 48,
                        color: const Color(0xFF2196F3), // Electric blue color
                      ),
                    ],
                  ),
                ),
              ),
              // Content on top of background
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Units',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode
                          ? Colors.grey.shade300
                          : Colors.grey.shade700,
                    ),
                  ),
                  Text(
                    '${_unitValue.toStringAsFixed(1)} kWh',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: primaryColor,
                      letterSpacing: -0.5,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // Improved slider design
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4.0,
            thumbShape: RoundSliderThumbShape(
              enabledThumbRadius: 10.0,
              pressedElevation: 6.0,
            ),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
            activeTrackColor: primaryColor,
            inactiveTrackColor: primaryColor.withAlpha(50),
            thumbColor: primaryColor,
            overlayColor: primaryColor.withAlpha(30),
          ),
          child: Slider(
            value: _unitValue,
            min: _minUnit,
            max: _maxUnit,
            divisions: (_maxUnit - _minUnit).toInt() * 2,
            onChanged: (value) {
              setState(() {
                _unitValue = value;
              });
            },
          ),
        ),

        // Min and max labels
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_minUnit.toInt()} kWh',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${_maxUnit.toInt()} kWh',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // Quick selection buttons with improved design
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildQuickSelectButton('5 kWh', primaryColor, () {
              setState(() {
                _unitValue = 5.0;
              });
            }),
            _buildQuickSelectButton('10 kWh', primaryColor, () {
              setState(() {
                _unitValue = 10.0;
              });
            }),
            _buildQuickSelectButton('20 kWh', primaryColor, () {
              setState(() {
                _unitValue = 20.0;
              });
            }),
            _buildQuickSelectButton('30 kWh', primaryColor, () {
              setState(() {
                _unitValue = 30.0;
              });
            }),
          ],
        ),
      ],
    );
  }

  // Amount-based configuration with enhanced money-themed design
  Widget _buildAmountConfiguration(
      Color primaryColor, Connector connector, bool isDarkMode) {
    final totalWithGST = _calculateTotalWithGST(_amountValue);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Set Charging Amount',
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
            letterSpacing: -0.3,
          ),
        ),
        const SizedBox(height: 8),

        // Total amount display including tax with money symbol background
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppThemes.primaryColor.withAlpha(
                217), // App theme green background for money (85% opacity)
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppThemes.primaryColor
                  .withAlpha(180), // Darker app theme green border
              width: 1,
            ),
          ),
          child: Stack(
            children: [
              // Background money symbols
              Positioned.fill(
                child: Opacity(
                  opacity:
                      0.15, // Slightly more visible white rupee symbols - opacity is fine here as it's used with Opacity widget
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Icon(
                        Icons.currency_rupee,
                        size: 40,
                        color: Colors.white, // White currency symbol
                      ),
                      Icon(
                        Icons.currency_rupee,
                        size: 40,
                        color: Colors.white, // White currency symbol
                      ),
                      Icon(
                        Icons.currency_rupee,
                        size: 40,
                        color: Colors.white, // White currency symbol
                      ),
                      Icon(
                        Icons.currency_rupee,
                        size: 40,
                        color: Colors.white, // White currency symbol
                      ),
                      Icon(
                        Icons.currency_rupee,
                        size: 40,
                        color: Colors.white, // White currency symbol
                      ),
                    ],
                  ),
                ),
              ),
              // Content on top of background
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Including Tax',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white, // White text on green background
                    ),
                  ),
                  Text(
                    '₹${totalWithGST.toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white, // White text for amount
                      letterSpacing: -0.5,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // Improved slider design
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4.0,
            thumbShape: RoundSliderThumbShape(
              enabledThumbRadius: 10.0,
              pressedElevation: 6.0,
            ),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
            activeTrackColor: primaryColor,
            inactiveTrackColor: primaryColor.withAlpha(50),
            thumbColor: primaryColor,
            overlayColor: primaryColor.withAlpha(30),
          ),
          child: Slider(
            value: _amountValue,
            min: _minAmount,
            max: _maxAmount,
            divisions: ((_maxAmount - _minAmount) / 50).toInt(),
            onChanged: (value) {
              setState(() {
                _amountValue = value;
              });
              _updateValueInputFromSlider();
              // Hidden calculation: automatically calculate units from amount
              _performHiddenCalculation(value, connector);
            },
          ),
        ),

        // Min and max labels
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '₹${_minAmount.toInt()}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '₹${_maxAmount.toInt()}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // Quick selection buttons with wallet balance validation
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildQuickSelectButton('₹100', primaryColor, () {
              final newAmount = 100.0;
              if (newAmount <= _maxAmount) {
                setState(() {
                  _amountValue = newAmount;
                });
                _updateValueInputFromSlider();
                _performHiddenCalculation(newAmount, connector);
              }
            }),
            _buildQuickSelectButton('₹200', primaryColor, () {
              final newAmount = 200.0;
              if (newAmount <= _maxAmount) {
                setState(() {
                  _amountValue = newAmount;
                });
                _updateValueInputFromSlider();
                _performHiddenCalculation(newAmount, connector);
              }
            }),
            _buildQuickSelectButton('₹500', primaryColor, () {
              final newAmount = 500.0;
              if (newAmount <= _maxAmount) {
                setState(() {
                  _amountValue = newAmount;
                });
                _updateValueInputFromSlider();
                _performHiddenCalculation(newAmount, connector);
              }
            }),
            _buildQuickSelectButton('Max', primaryColor, () {
              setState(() {
                _amountValue = _maxAmount;
              });
              _updateValueInputFromSlider();
              _performHiddenCalculation(_maxAmount, connector);
            }),
          ],
        ),
      ],
    );
  }

  // Enhanced quick selection button with improved visuals
  Widget _buildQuickSelectButton(
      String label, Color primaryColor, VoidCallback onTap) {
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    // Check if this button is selected
    final bool isSelected = (label == '10 kWh' && _unitValue == 10.0) ||
        (label == '5 kWh' && _unitValue == 5.0) ||
        (label == '20 kWh' && _unitValue == 20.0) ||
        (label == '30 kWh' && _unitValue == 30.0) ||
        (label == '₹100' && _amountValue == 100.0) ||
        (label == '₹200' && _amountValue == 200.0) ||
        (label == '₹500' && _amountValue == 500.0) ||
        (label == 'Max' && _amountValue == _maxAmount);

    // Check if this amount button is disabled due to wallet limits
    bool isDisabled = false;
    if (_selectedOption == ChargingOptionType.amountBased) {
      if (label == '₹100' && 100.0 > _maxAmount) isDisabled = true;
      if (label == '₹200' && 200.0 > _maxAmount) isDisabled = true;
      if (label == '₹500' && 500.0 > _maxAmount) isDisabled = true;
    }

    return InkWell(
      onTap: isDisabled ? null : onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? primaryColor.withAlpha(20)
              : isDisabled
                  ? Colors.grey.withAlpha(10)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: primaryColor.withAlpha(51), // 20% opacity
                    blurRadius: 4,
                    spreadRadius: 1,
                    offset: const Offset(0, 1),
                  )
                ]
              : null,
          border: Border.all(
            color: isSelected
                ? primaryColor
                : isDisabled
                    ? Colors.grey.withAlpha(40)
                    : isDarkMode
                        ? Colors.grey.withAlpha(60)
                        : Colors.grey.withAlpha(100),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Text(
          label == 'Max' ? 'Max (₹${_maxAmount.toStringAsFixed(0)})' : label,
          style: TextStyle(
            color: isSelected
                ? primaryColor
                : isDisabled
                    ? Colors.grey.shade400
                    : isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 12,
            letterSpacing: -0.2,
          ),
        ),
      ),
    );
  }
}
